#define _GNU_SOURCE
#include "send_utils.h"
#include "concurrent_queue.h"
#include "config_loader.h"
#include "crypto_utils.h"
#include "hex_utils.h"
#include "include/sitp_lib.h"
#include "log.h"
#include "oake.h"
#include "packet_utils.h"
#include <arpa/inet.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <netinet/udp.h>
#include <pthread.h>
#include <stddef.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

static queue_t *sitp_send_queue = NULL;
static pthread_t sender_thread_id;
static volatile int keep_sender_thread_running = 1;

static void *sitp_sender_thread_func(void *arg) {
  (void)arg;

  log_info("SITP sender thread started");
  while (keep_sender_thread_running) {
    uint8_t *packet_buffer = NULL;
    size_t packet_len = 0;    if (queue_dequeue(sitp_send_queue, &packet_buffer, &packet_len) == 0) {
      if (packet_buffer != NULL) {
        // Check for poison pill: 1-byte buffer with 0xFF
        if (packet_len == 1 && packet_buffer[0] == 0xFF) {
          log_info("Sender thread: Received poison pill, stopping...");
          free(packet_buffer);
          break;
        }

        int send_result = sitp_lib_send(packet_buffer, packet_len);
        if (send_result != 0) {
          log_error("sitp_sender_thread_func: sitp_lib_send reported error "
                    "(code: %d) for packet of size %zu",
                    send_result, packet_len);
        } else {
          const task_config_t *config = get_global_config();
          if (config && config->sitp_task.send_delay > 0) {
            usleep(config->sitp_task.send_delay);
          }
        }
        free(packet_buffer);
      }    } else {
      // Error with queue_dequeue
      if (!keep_sender_thread_running) {
        log_info("Sender thread: Exiting due to shutdown signal (dequeue "
                 "returned error)");
        break;
      }
      log_error("sitp_sender_thread_func: queue_dequeue failed. "
                "Retrying if running");
    }
  }
  log_info("SITP sender thread stopped");
  return NULL;
}

int init_sitp_sender(size_t queue_capacity) {
  log_info("Initializing SITP sender with queue capacity %zu...",
           queue_capacity);
  if (sitp_send_queue != NULL) {
    log_error("init_sitp_sender: SITP sender already initialized");
    return -1;
  }

  sitp_send_queue = queue_create(queue_capacity);
  if (sitp_send_queue == NULL) {
    log_error("init_sitp_sender: Failed to create SITP send queue");
    return -1;
  }

  keep_sender_thread_running = 1;
  if (pthread_create(&sender_thread_id, NULL, sitp_sender_thread_func, NULL) !=
      0) {
    log_error("init_sitp_sender: Failed to create SITP sender thread");
    queue_destroy(sitp_send_queue);
    sitp_send_queue = NULL;
    return -1;
  }
  log_info("SITP sender initialized successfully");
  return 0;
}

void cleanup_sitp_sender(void) {
  log_info("Cleaning up SITP sender...");
  if (sitp_send_queue == NULL && !keep_sender_thread_running) {
    log_info("SITP sender already cleaned up or not initialized");
    return;
  }
  if (keep_sender_thread_running) {
    keep_sender_thread_running = 0;

    // Send poison pill to unblock and signal the sender thread to stop
    // Use a 1-byte buffer with 0xFF as the poison pill
    uint8_t poison_pill = 0xFF;
    log_debug("Sending poison pill to sender thread...");

    if (queue_enqueue(sitp_send_queue, &poison_pill, 1) != 0) {
      log_error("cleanup_sitp_sender: Failed to send poison pill. "
                "Thread might be stuck");
    }

    log_debug("Waiting for sender thread to stop...");
    if (pthread_join(sender_thread_id, NULL) != 0) {
      log_error("cleanup_sitp_sender: Failed to join SITP sender thread. "
                "It might be stuck");
    } else {
      log_debug("SITP sender thread joined successfully");
    }
  }

  if (sitp_send_queue != NULL) {
    queue_destroy(sitp_send_queue);
    sitp_send_queue = NULL;
    log_debug("SITP send queue destroyed");
  }
  log_info("SITP sender cleanup complete");
}

// Helper function to encrypt packet data if encryption is enabled
// Returns encrypted buffer (encrypted_data + MAC) or NULL on failure
// Sets *out_encrypted_len to the length of the encrypted buffer
static uint8_t *encrypt_packet_if_enabled(const uint8_t *buffer, size_t len,
                                          const char *dst_ip,
                                          size_t *out_encrypted_len) {
  const task_config_t *config = get_global_config();
  if (config == NULL) {
    log_error("encrypt_packet_if_enabled: Global config not loaded");
    return NULL;
  }

  // Check if encryption is enabled
  if (config->csl.encrypt != 1) {
    // Encryption disabled, return NULL to indicate no encryption
    *out_encrypted_len = 0;
    return NULL;
  }

  log_debug("Encryption enabled, processing packet...");

  oake_generic_data_t *current_oake_data = oake_get_data_by_ip(dst_ip);
  if (current_oake_data == NULL) {
    log_error("encrypt_packet_if_enabled: Failed to get OAKE data for IP %s",
              dst_ip);
    return NULL;
  }

  // Check if remote_key is available
  if (current_oake_data->remote_key == NULL) {
    log_error("encrypt_packet_if_enabled: Remote key not available for "
              "encryption");
    return NULL;
  }

  // Allocate buffers for encryption
  // Assume ciphertext might be slightly larger than plaintext (add some
  // padding)
  unsigned int ciphertext_buffer_len = len + 64; // Add some extra space
  uint8_t *ciphertext_buffer = (uint8_t *)malloc(ciphertext_buffer_len);
  uint8_t mac_buffer[16]; // MAC is typically 16 bytes

  if (ciphertext_buffer == NULL) {
    log_error("encrypt_packet_if_enabled: Failed to allocate memory for "
              "encryption buffers");
    return NULL;
  }

  // Call encryption function
  SM_RV encrypt_result = crypto_encrypt_data(
      current_oake_data->remote_key, buffer, (unsigned int)len,
      ciphertext_buffer, &ciphertext_buffer_len, mac_buffer);

  if (encrypt_result != CSR_OK) {
    log_error("encrypt_packet_if_enabled: Encryption failed with error: 0x%X",
              encrypt_result);
    free(ciphertext_buffer);
    return NULL;
  }

  // Create combined buffer: encrypted_data + MAC
  size_t combined_len = ciphertext_buffer_len + 16; // 16 bytes for MAC
  uint8_t *encrypted_buffer = (uint8_t *)malloc(combined_len);
  if (encrypted_buffer == NULL) {
    log_error("encrypt_packet_if_enabled: Failed to allocate memory for "
              "combined encrypted buffer");
    free(ciphertext_buffer);
    return NULL;
  }

  // Copy encrypted data and MAC
  memcpy(encrypted_buffer, ciphertext_buffer, ciphertext_buffer_len);
  memcpy(encrypted_buffer + ciphertext_buffer_len, mac_buffer, 16);

  // Free intermediate buffer
  free(ciphertext_buffer);

  *out_encrypted_len = combined_len;
  log_debug("Packet encrypted successfully. Original size: %zu, Encrypted+MAC "
            "size: %zu",
            len, combined_len);

  return encrypted_buffer;
}

// Helper function to create a new buffer with padding at both ends.
static uint8_t *create_padded_buffer(const uint8_t *original_buffer,
                                     size_t original_len, size_t padding_amount,
                                     size_t *out_new_len) {
  *out_new_len = original_len + (2 * padding_amount);
  uint8_t *padded_buffer = (uint8_t *)malloc(*out_new_len);

  if (padded_buffer == NULL) {
    log_error("create_padded_buffer: Failed to allocate memory for padded buffer "
              "(size: %zu)",
              *out_new_len);
    *out_new_len = 0;
    return NULL;
  }

  memset(padded_buffer, 0, padding_amount); // Prefix padding
  memcpy(padded_buffer + padding_amount, original_buffer,
         original_len); // Original data
  memset(padded_buffer + padding_amount + original_len, 0,
         padding_amount); // Suffix padding

  return padded_buffer;
}

int process_and_send_packet(uint8_t *buffer, size_t len) {
  log_debug("--- Queue SITP Packet Details ---");

  // Get source and destination IP addresses from packet
  char src_ip[INET_ADDRSTRLEN];
  char dst_ip[INET_ADDRSTRLEN];
  print_ip_and_ports(buffer, len, src_ip, dst_ip, NULL, NULL);

  print_payload(buffer, len);
  log_debug("----------------------------");

  const task_config_t *config = get_global_config();
  if (config == NULL) {
    log_error("process_and_send_packet: Global config not loaded");
    return -1;
  }

  if (sitp_send_queue == NULL) {
    log_error("process_and_send_packet: SITP send queue is not "
              "initialized. Dropping packet");
    return -1;
  }

  uint8_t *data_to_process = buffer;
  size_t data_len = len;
  uint8_t *encrypted_buffer = NULL;

  // Try to encrypt the packet if encryption is enabled
  size_t encrypted_len = 0;
  encrypted_buffer =
      encrypt_packet_if_enabled(buffer, len, dst_ip, &encrypted_len);

  if (encrypted_buffer != NULL) {
    // Encryption was performed successfully
    data_to_process = encrypted_buffer;
    data_len = encrypted_len;
  } else if (config->csl.encrypt == 1) {
    // Encryption was enabled but failed
    log_error("process_and_send_packet: Encryption failed. Dropping packet");
    return -1;
  }
  // If encrypted_buffer is NULL and encrypt is 0, continue with original data

  // Create 32-byte array with source IP (16 bytes) + destination IP (16 bytes)
  uint8_t ip_header[32];
  memset(ip_header, 0, 32); // Initialize with zeros

  // Copy source IP string to the first 16 bytes
  size_t src_ip_len = strlen(src_ip);
  if (src_ip_len > 0 && src_ip_len < 16) {
    memcpy(ip_header, src_ip, src_ip_len);
  }

  // Copy destination IP string to the next 16 bytes
  size_t dst_ip_len = strlen(dst_ip);
  if (dst_ip_len > 0 && dst_ip_len < 16) {
    memcpy(ip_header + 16, dst_ip, dst_ip_len);
  }

  // Create new buffer with IP header (src_ip + dst_ip) prepended to data
  size_t new_data_len = 32 + data_len;
  uint8_t *data_with_ip = (uint8_t *)malloc(new_data_len);
  if (data_with_ip == NULL) {
    log_error("process_and_send_packet: Failed to allocate memory for "
              "data with IP header");
    if (encrypted_buffer != NULL) {
      free(encrypted_buffer);
    }
    return -1;
  }

  // Copy IP header (src_ip + dst_ip) and data
  memcpy(data_with_ip, ip_header, 32);
  memcpy(data_with_ip + 32, data_to_process, data_len);

  size_t padding_to_add = config->sitp_task.pkt_trim_bytes;
  size_t new_padded_len = 0;
  int enqueue_result = -1;

  uint8_t *padded_buffer = create_padded_buffer(
      data_with_ip, new_data_len, padding_to_add, &new_padded_len);

  if (padded_buffer == NULL) {
    free(data_with_ip);
    if (encrypted_buffer != NULL) {
      free(encrypted_buffer);
    }
    return -1;
  }

  // queue_enqueue is expected to make a copy of the data in padded_buffer.
  enqueue_result =
      queue_enqueue(sitp_send_queue, padded_buffer, new_padded_len);

  if (enqueue_result != 0) {
    log_error("process_and_send_packet: Failed to enqueue packet (padded size: "
              "%zu). Dropping packet",
              new_padded_len);
    free(padded_buffer); // Free if enqueue failed and didn't take ownership.
    free(data_with_ip);
    if (encrypted_buffer != NULL) {
      free(encrypted_buffer);
    }
    return -1;
  }

  // Free allocated buffers
  free(padded_buffer);
  free(data_with_ip);
  if (encrypted_buffer != NULL) {
    free(encrypted_buffer);
  }

  return 0;
}

int process_and_send_packet_plaintext(uint8_t *buffer, size_t len) {
  log_debug("--- Queue SITP Packet Details (No Encryption) ---");
  print_payload(buffer, len);
  log_debug("----------------------------------------------------");

  const task_config_t *config = get_global_config();
  if (config == NULL) {
    log_error("process_and_send_packet_plaintext: Global config not loaded");
    return -1;
  }

  if (sitp_send_queue == NULL) {
    log_error("process_and_send_packet_plaintext: SITP send queue is not "
              "initialized. Dropping packet");
    return -1;
  }

  // Use original buffer directly (no encryption)
  uint8_t *data_to_process = buffer;
  size_t data_len = len;

  size_t padding_to_add = config->sitp_task.pkt_trim_bytes;
  size_t new_padded_len = 0;
  int enqueue_result = -1;

  uint8_t *padded_buffer = create_padded_buffer(
      data_to_process, data_len, padding_to_add, &new_padded_len);

  if (padded_buffer == NULL) {
    return -1;
  }

  // queue_enqueue is expected to make a copy of the data in padded_buffer.
  enqueue_result =
      queue_enqueue(sitp_send_queue, padded_buffer, new_padded_len);

  if (enqueue_result != 0) {
    log_error("process_and_send_packet_plaintext: Failed to enqueue packet "
              "(padded size: %zu). Dropping packet",
              new_padded_len);
    free(padded_buffer); // Free if enqueue failed and didn't take ownership.
    return -1;
  }

  // Free allocated buffer
  free(padded_buffer);

  return 0;
}
